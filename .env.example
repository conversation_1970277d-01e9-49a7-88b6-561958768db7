# Server
PORT=3000

# Weaviate
WEAVIATE_HOST=localhost
WEAVIATE_HTTP_PORT=8080
WEAVIATE_GRPC_PORT=50051
WEAVIATE_SECURE=false
WEAVIATE_API_KEY=

# OpenAI
OPENAI_API_KEY=

# Google
GOOGLE_API_KEY=

# Model Configuration
# Primary model cho ecommerce agent (mặc định: gemini-2.0-flash)
LLM_MODEL=gemini-2.0-flash
# Primary model cho RAG agent (mặc định: gemini-2.0-flash)
RAG_LLM_MODEL=gemini-2.0-flash

# OpenRouter
OPENROUTER_API_KEY=

# Supabase
SUPABASE_URL=https://vhduizefoibsipsiraqf.supabase.co
SUPABASE_ANON_KEY=
# Lấy service key từ Supabase Dashboard > Settings > API > service_role key
# Service key có quyền bypass Row Level Security (RLS) và chỉ nên sử dụng ở phía server
SUPABASE_SERVICE_KEY=

# PostgreSQL
PG_CONNECTION_STRING=postgresql://postgres:postgres@localhost:5432/postgres

# Langfuse
LANGFUSE_PUBLIC_KEY=
LANGFUSE_SECRET_KEY=
LANGFUSE_BASEURL=https://cloud.langfuse.com

# Mastra configuration
USE_MASTRA_CLIENT=true
MASTRA_SERVER_URL=http://localhost:4111
