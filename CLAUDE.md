# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Commands

### Development
- `npm run dev` - Start Express server in development mode
- `npm run dev:worker` - Start worker process in development mode
- `npm run dev:mastra` - Start Mastra development server

### Testing
- `npm test` - Run Jest tests
- `npm run test:watch` - Run tests in watch mode
- `npm run test:coverage` - Run tests with coverage report

### Building and Production
- `npm run build` - Build TypeScript to dist/ directory
- `npm run start` - Start production server from dist/
- `npm run start:worker` - Start production worker from dist/

### Code Quality
- `npm run lint` - Run TypeScript type checking (no emit)
- `npm run clean` - Remove dist/ directory

## Architecture Overview

This is a Node.js/Express backend for an AI chatbot system serving an e-commerce platform (Thọ Trần Shop). The architecture uses a multi-process design with separate server and worker processes.

### Core Components

**Express Server (`src/server.ts`)**
- Main HTTP API server
- Handles chatbot interactions via REST endpoints
- Integrates with Mastra AI agent framework
- Includes Bull Board dashboard for queue monitoring

**Worker Process (`src/worker.ts`)**
- Dedicated BullMQ worker for background tasks
- Handles product synchronization from external platforms
- Runs independently from the main server

**Mastra AI Framework (`src/mastra/`)**
- Agent-based AI system with tool calling capabilities
- Configured agents for customer service and product recommendations
- Memory system with PostgreSQL storage
- Tools for product search, order management, FAQ handling

### Data Flow

1. **Product Synchronization**: External platforms (Haravan, Sapo) → BullMQ → Worker → Database
2. **Customer Interactions**: Frontend → Express API → Mastra Agents → Tools → Database/External APIs
3. **AI Processing**: User message → Agent → Tool calls → Response generation

### Key Services

**Queue System (`src/services/queue/`)**
- BullMQ with Redis/Dragonfly for job processing
- Product sync, follow-up messages, and health monitoring
- Configurable worker concurrency and retry logic

**Platform Sync (`src/services/sync/`)**
- Adapter pattern for different e-commerce platforms
- Handles product data mapping and image processing
- Batch operations for efficient synchronization

**Database Services**
- PostgreSQL for structured data (products, orders, customers)
- Supabase for authentication and file storage
- Weaviate for vector search and AI memory

**AI Tools (`src/mastra/tools/`)**
- Product search and recommendations
- Order management (create, track, cancel)
- Customer support (FAQs, lead collection)
- Promotion and shipping information

### Environment Configuration

The system supports multiple operation modes:
- `DISABLE_BULLMQ=true` - Disable background job processing
- `WORKER_MODE=true` - Run only worker process
- `INTEGRATED_MODE=true` - Run server with integrated workers
- `USE_MASTRA_CLIENT=true` - Use Mastra via client (recommended for telemetry)

### Development Workflow

1. **Local Development**: Run `npm run dev` for server and `npm run dev:worker` for background jobs
2. **Testing**: Use `npm test` - tests are located in `tests/` directory
3. **Type Checking**: Always run `npm run lint` before committing
4. **Building**: Use `npm run build` for production deployment

### Key Dependencies

- **Mastra**: AI agent framework with tool calling
- **BullMQ**: Job queue system with Redis
- **Express**: Web framework
- **TypeScript**: Type safety
- **PostgreSQL**: Primary database
- **Supabase**: Backend-as-a-service
- **Weaviate**: Vector database for AI

### Error Handling

The system implements comprehensive error handling:
- Graceful shutdown for both server and worker processes
- Health check endpoints for monitoring
- Retry mechanisms for failed jobs
- Detailed logging for debugging