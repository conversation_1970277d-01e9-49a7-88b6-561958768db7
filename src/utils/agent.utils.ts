/**
 * Utility functions for agent operations
 */

/**
 * Interface for agent response validation
 */
export interface AgentResponse {
  text: string;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  toolCalls?: any[];
}

/**
 * Validate agent response to ensure it's properly formatted
 */
export const validateAgentResponse = (response: any): AgentResponse => {
  if (!response) {
    throw new Error("Agent response is null or undefined");
  }

  // Kiểm tra text response - có thể text nằm ở property khác
  let textContent = response.text;

  // Nếu không có text, thử tìm ở các property khác
  if (!textContent) {
    textContent = response.content || response.message || response.output;
  }

  if (!textContent || typeof textContent !== 'string' || textContent.trim().length === 0) {
    throw new Error(`Agent response text is empty or invalid. Available keys: ${Object.keys(response).join(', ')}`);
  }

  // Extract content from <reply></reply> XML tags
  const extractedContent = extractReplyContent(textContent);

  // Cập nhật response với text đã extract
  response.text = extractedContent;

  // Kiểm tra token usage nếu có
  if (response.usage) {
    const { promptTokens, completionTokens, totalTokens } = response.usage;
    
    if (typeof promptTokens !== 'number' || isNaN(promptTokens) || promptTokens < 0) {
      throw new Error(`Invalid promptTokens: ${promptTokens}`);
    }
    
    if (typeof completionTokens !== 'number' || isNaN(completionTokens) || completionTokens < 0) {
      throw new Error(`Invalid completionTokens: ${completionTokens}`);
    }
    
    if (typeof totalTokens !== 'number' || isNaN(totalTokens) || totalTokens < 0) {
      throw new Error(`Invalid totalTokens: ${totalTokens}`);
    }

    // Kiểm tra logic tổng token
    if (totalTokens !== promptTokens + completionTokens) {
      console.warn(`⚠️ Token calculation mismatch: ${totalTokens} !== ${promptTokens} + ${completionTokens}`);
    }
  }

  return response as AgentResponse;
};

/**
 * Check if error is retryable
 */
export const isRetryableError = (error: any): boolean => {
  if (!error) return false;

  const errorMessage = error.message?.toLowerCase() || '';
  const errorCode = error.code;

  // Network errors
  if (errorMessage.includes('network') || 
      errorMessage.includes('timeout') || 
      errorMessage.includes('connection')) {
    return true;
  }

  // Rate limiting
  if (errorMessage.includes('rate limit') || 
      errorMessage.includes('too many requests') ||
      errorCode === 429) {
    return true;
  }

  // Server errors (5xx)
  if (errorCode >= 500 && errorCode < 600) {
    return true;
  }

  // Token usage errors
  if (errorMessage.includes('nan') ||
      errorMessage.includes('invalid token usage')) {
    return true;
  }

  // Empty response errors (có thể do model overload)
  if (errorMessage.includes('empty') ||
      errorMessage.includes('invalid') ||
      errorMessage.includes('text is empty')) {
    return true;
  }

  // Model errors that might be temporary
  if (errorMessage.includes('model') &&
      (errorMessage.includes('unavailable') ||
       errorMessage.includes('overloaded'))) {
    return true;
  }

  return false;
};

/**
 * Create fallback response when all retries fail
 * KHÔNG gửi tin nhắn lỗi cho khách hàng - chỉ return null để hệ thống xử lý
 */
export const createFallbackResponse = (originalError: Error): AgentResponse | null => {
  console.error('❌ Tất cả retry đã thất bại, không gửi tin nhắn cho khách hàng:', originalError);

  // Return null để báo hiệu không gửi tin nhắn nào cho khách hàng
  return null;
};

/**
 * Log agent performance metrics
 */
export const logAgentMetrics = (
  attempt: number,
  success: boolean,
  duration: number,
  usage?: AgentResponse['usage'],
  error?: Error
) => {
  const timestamp = new Date().toISOString();
  const metrics = {
    timestamp,
    attempt,
    success,
    duration,
    usage,
    error: error?.message,
  };

  if (success) {
  } else {
    console.error(`❌ Agent failure metrics:`, metrics.error);
  }
};

/**
 * Calculate retry delay with jitter
 */
export const calculateRetryDelay = (
  attempt: number,
  baseDelay: number = 1000,
  maxDelay: number = 10000,
  jitter: boolean = true
): number => {
  const exponentialDelay = baseDelay * Math.pow(2, attempt);
  const cappedDelay = Math.min(exponentialDelay, maxDelay);

  if (jitter) {
    // Add random jitter (±25%)
    const jitterAmount = cappedDelay * 0.25;
    const randomJitter = (Math.random() - 0.5) * 2 * jitterAmount;
    return Math.max(0, cappedDelay + randomJitter);
  }

  return cappedDelay;
};

/**
 * Extract text content from <reply></reply> XML tags
 * Falls back to extracting from <final_answer></final_answer> if reply tags not found
 * Returns original text if no XML tags found
 */
export const extractReplyContent = (text: string): string => {
  if (!text || typeof text !== 'string') {
    return '';
  }

  // Try to extract from <reply></reply> tags first
  const replyMatch = text.match(/<reply>([\s\S]*?)<\/reply>/i);
  if (replyMatch && replyMatch[1]) {
    return replyMatch[1].trim();
  }

  // Fallback to <final_answer></final_answer> tags for backward compatibility
  const finalAnswerMatch = text.match(/<final_answer>([\s\S]*?)<\/final_answer>/i);
  if (finalAnswerMatch && finalAnswerMatch[1]) {
    return finalAnswerMatch[1].trim();
  }

  // If no XML tags found, return original text
  return text.trim();
};
