import { Response } from 'express';

/**
 * Tr<PERSON> về response thành công
 */
export const sendSuccess = (res: Response, data: any, message = 'Thành công', statusCode = 200) => {
  return res.status(statusCode).json({
    success: true,
    message,
    data
  });
};

/**
 * Trả về response lỗi
 */
export const sendError = (res: Response, message: string, statusCode = 500, error?: any) => {
  return res.status(statusCode).json({
    success: false,
    message,
    error: process.env.NODE_ENV === 'production' ? undefined : error
  });
};

/**
 * Trả về response lỗi 400 Bad Request
 */
export const sendBadRequest = (res: Response, message = '<PERSON>êu cầu không hợp lệ', error?: any) => {
  return sendError(res, message, 400, error);
};

/**
 * Trả về response lỗi 401 Unauthorized
 */
export const sendUnauthorized = (res: Response, message = 'Không được phép truy cập', error?: any) => {
  return sendError(res, message, 401, error);
};

/**
 * Tr<PERSON> về response lỗi 404 Not Found
 */
export const sendNotFound = (res: Response, message = 'Không tìm thấy tài nguyên', error?: any) => {
  return sendError(res, message, 404, error);
};

/**
 * Extract nội dung từ thẻ <reply> hoặc <final_answer> trong response của AI
 * Bỏ qua phần <thinking> và các nội dung khác để chỉ lấy final answer gửi cho khách hàng
 * Luôn luôn xóa tag <thinking>, <reply> và <final_answer> để đảm bảo response thân thiện với user
 * Xử lý cả trường hợp tag không được đóng đúng cách
 */
export const extractFinalAnswer = (aiResponse: string): string => {
  if (!aiResponse || typeof aiResponse !== 'string') {
    return aiResponse || '';
  }

  let cleanedResponse = aiResponse;

  // Bước 1: Xóa toàn bộ nội dung trong thẻ <thinking>
  cleanedResponse = cleanedResponse.replace(/<thinking>[\s\S]*?<\/thinking>/gi, '');

  // Bước 2: Xóa các tag thinking rỗng hoặc không đóng đúng cách
  cleanedResponse = cleanedResponse.replace(/<\/?thinking[^>]*>/gi, '');

  // Bước 3: Tìm và xử lý thẻ <reply> trước (ưu tiên)
  // Trường hợp 1: Tag <reply> đóng đúng cách
  const replyMatch = cleanedResponse.match(/<reply>([\s\S]*?)<\/reply>/i);
  if (replyMatch && replyMatch[1]) {
    return replyMatch[1].trim();
  }

  // Trường hợp 2: Tag <reply> mở nhưng không đóng
  const openReplyMatch = cleanedResponse.match(/<reply>\s*([\s\S]*)/i);
  if (openReplyMatch && openReplyMatch[1]) {
    let content = openReplyMatch[1];
    content = content.replace(/<\/reply>/gi, '');
    return content.trim();
  }

  // Bước 4: Fallback - Tìm và xử lý thẻ <final_answer>
  // Trường hợp 1: Tag đóng đúng cách
  const finalAnswerMatch = cleanedResponse.match(/<final_answer>([\s\S]*?)<\/final_answer>/i);
  if (finalAnswerMatch && finalAnswerMatch[1]) {
    return finalAnswerMatch[1].trim();
  }

  // Trường hợp 2: Tag mở nhưng không đóng (hoặc đóng sai)
  const openTagMatch = cleanedResponse.match(/<final_answer>\s*([\s\S]*)/i);
  if (openTagMatch && openTagMatch[1]) {
    let content = openTagMatch[1];
    content = content.replace(/<\/final_answer>/gi, '');
    return content.trim();
  }

  // Bước 5: Xóa các tag còn sót lại
  cleanedResponse = cleanedResponse.replace(/<\/?reply[^>]*>/gi, '');
  cleanedResponse = cleanedResponse.replace(/<\/?final_answer[^>]*>/gi, '');

  // Loại bỏ khoảng trắng thừa và trả về
  return cleanedResponse.trim();
};
