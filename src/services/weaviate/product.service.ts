import { client } from "../../config/weaviate";
import weaviate, {
  vectorizer,
  dataType,
  configure,
  Filters,
} from "weaviate-client";
import { imageUrlToBase64, isValidImageUrl, PLACEHOLDER_IMAGE_BASE64 } from "../../utils/image.utils";

// ========== OPTIMIZATION LAYER ==========

// Constants for optimization
const MAX_CONCURRENT_IMAGES = 10;

// Optimized image processing with retry (no cache for multi-tenant)
async function processImageWithRetry(
  imageUrl: string, 
  maxRetries: number = 3
): Promise<{ base64: string; isValid: boolean }> {
  if (!imageUrl?.trim()) {
    return { base64: PLACEHOLDER_IMAGE_BASE64, isValid: false };
  }

  // Retry logic with exponential backoff
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const isValid = await isValidImageUrl(imageUrl);
      if (!isValid) {
        return { base64: PLACEHOLDER_IMAGE_BASE64, isValid: false };
      }

      const base64 = await imageUrlToBase64(imageUrl, PLACEHOLDER_IMAGE_BASE64);
      return { base64, isValid: true };
      
    } catch (error) {
      console.warn(`Attempt ${attempt}/${maxRetries} failed for ${imageUrl}:`, error);
      
      if (attempt === maxRetries) {
        return { base64: PLACEHOLDER_IMAGE_BASE64, isValid: false };
      }
      
      // Exponential backoff
      await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
    }
  }
  
  return { base64: PLACEHOLDER_IMAGE_BASE64, isValid: false };
}

// Optimized filter creation
function createSearchFilter(tenant: any, bot_id?: string) {
  const activeFilter = tenant.filter.byProperty("is_active").equal(true);
  
  if (bot_id) {
    return Filters.and(
      tenant.filter.byProperty("bot_id").equal(bot_id),
      activeFilter
    );
  }
  
  return activeFilter;
}

/**
 * Khởi tạo collection Products trong Weaviate
 */
export const initProductsCollection = async () => {
  try {
    // Kiểm tra xem collection đã tồn tại chưa
    const collections = await client.collections.get("Products");
    const productsExists = await collections.exists();

    // Nếu collection đã tồn tại, xóa và tạo lại để áp dụng cấu hình mới
    if (productsExists) {
      // Xóa collection cũ để tạo lại với cấu hình mới
      await client.collections.delete("Products");
    }

    // Tạo collection mới
    await client.collections.create({
      name: "Products",
      multiTenancy: weaviate.configure.multiTenancy({ enabled: true }),
      properties: [
        { name: "name", dataType: dataType.TEXT },
        { name: "price", dataType: dataType.NUMBER },
        { name: "stock", dataType: dataType.NUMBER },
        { name: "description", dataType: dataType.TEXT },
        { name: "image_url", dataType: dataType.TEXT },
        {
          name: "image",
          dataType: dataType.BLOB,
        },
        { name: "tenant_id", dataType: dataType.TEXT },
        {
          name: "bot_id",
          dataType: dataType.TEXT,
        },
        { name: "product_id", dataType: dataType.TEXT },
        { name: "is_active", dataType: dataType.BOOLEAN },
      ],
      vectorizers: [
        vectorizer.multi2VecClip({
          name: "product_image",
          imageFields: [{ name: "image", weight: 0.9 }],
          textFields: [
            { name: "name", weight: 0.3 },
            { name: "description", weight: 0.3 },
          ],
        }),
      ],
    });
    return {
      success: true,
      message: 'Đã tạo collection "Products" thành công',
    };
  } catch (error: any) {
    return {
      success: false,
      message: `Lỗi khi tạo collection "Products": ${error?.message || "Lỗi không xác định"}`,
    };
  }
};

/**
 * Thêm một sản phẩm mới vào collection Products
 * Tự động chuyển đổi image_url sang base64 để tối ưu hóa tìm kiếm nearImage
 */
export const addProduct = async ({
  name,
  price,
  stock,
  description,
  image_url,
  tenant_id,
  bot_id,
  product_id,
}: {
  name: string;
  price: number;
  stock: number;
  description: string;
  image_url: string;
  tenant_id: string;
  bot_id: string;
  product_id: string;
}) => {
  try {
    const productsCollection = await client.collections.get("Products");

    // Optimized image processing with retry
    const { base64: processedImageUrl } = await processImageWithRetry(image_url);

    // Thêm sản phẩm mới
    const result = await productsCollection.data.insert({
      properties: {
        name,
        price,
        stock,
        description,
        image_url, // Lưu URL gốc
        image: processedImageUrl, // Lưu base64 vào trường image để tìm kiếm
        tenant_id,
        bot_id,
        is_active: true,
        product_id,
      },
    });

    return { success: true, data: result };
  } catch (error: any) {
    return {
      success: false,
      message: `Lỗi khi thêm sản phẩm: ${error?.message || "Lỗi không xác định"}`,
    };
  }
};

/**
 * Thêm nhiều sản phẩm cùng lúc vào collection Products
 * Tự động chuyển đổi image_url sang base64 để tối ưu hóa tìm kiếm nearImage
 * @param products Mảng các đối tượng sản phẩm cần thêm
 */
export const addProducts = async (
  products: Array<{
    name: string;
    price: number;
    stock: number;
    description: string;
    image_url: string;
    tenant_id: string;
    bot_id: string;
    product_id: string;
  }>
) => {
  try {
    // Sử dụng client đã được tạo sẵn
    const productsCollection = await client.collections.get("Products");

    // Kiểm tra và tạo tenant nếu chưa tồn tại
    let tenant = await productsCollection.tenants.getByName(
      products[0].tenant_id
    );

    if (!tenant) {
      await productsCollection.tenants.create([
        { name: products[0].tenant_id },
      ]);
    }

    const tenantCollection = productsCollection.withTenant(
      products[0].tenant_id
    );

    // Optimized: Process images in parallel with concurrency control
    const imageUrls = products.map(p => p.image_url);
    const imageResults = await Promise.all(
      imageUrls.map(url => processImageWithRetry(url))
    );

    // Process products with optimized bot_id handling and processed images
    const processedProducts = products.map((product, index) => ({
      name: product.name,
      price: product.price,
      stock: product.stock,
      description: product.description,
      image_url: product.image_url,
      image: imageResults[index].base64,
      tenant_id: product.tenant_id,
      bot_id: product.bot_id,
      is_active: true,
      product_id: product.product_id,
    }));

    // Thêm nhiều sản phẩm đã xử lý cùng lúc
    const result = await tenantCollection?.data.insertMany(processedProducts);

    return { success: true, data: result };
  } catch (error: any) {
    return {
      success: false,
      message: `Lỗi khi thêm nhiều sản phẩm: ${error?.message || "Lỗi không xác định"}`,
    };
  }
};

/**
 * Tìm kiếm sản phẩm dựa trên nội dung
 * Chỉ thực hiện một trong hai phương thức tìm kiếm: text hoặc image
 * Nếu có image_url, sẽ ưu tiên tìm kiếm theo hình ảnh
 * Nếu không có image_url hoặc tìm kiếm hình ảnh thất bại, sẽ tìm kiếm theo text
 */
export const searchProducts = async (
  {
    query,
    image_url,
    bot_id,
    tenant_id,
  }: { query: string; image_url: string; bot_id: string; tenant_id: string },
  limit: number = 5
) => {
  try {
    // Sử dụng client đã được tạo sẵn
    const productsCollection = await client.collections.get("Products");
    const tenant = productsCollection.withTenant(tenant_id);
    let searchResult = null;

    const searchOptions = {
      limit: limit,
      returnMetadata: ["distance", "score", "certainty"],
      filters: createSearchFilter(tenant, bot_id),
      groupBy: {
        property: "product_id",
        objectsPerGroup: 1,
        numberOfGroups: limit,
      },
    };

    // Optimized image search with retry
    if (image_url && image_url.trim()) {
      try {
        const { base64: imageBase64 } = await processImageWithRetry(image_url);
        searchResult = await tenant.query.nearImage(imageBase64, searchOptions);

        if (searchResult && searchResult.objects?.length > 0) {
          return {
            success: true,
            data: {
              objects: searchResult.objects,
              searchType: "image",
              metadata: {
                total: searchResult.objects.length,
                limit
              }
            },
          };
        }
      } catch (imageError: any) {
        console.warn("Image search failed, fallback to text search:", imageError);
      }
    }

    // Nếu không có image_url hoặc tìm kiếm hình ảnh thất bại, tìm kiếm theo text
    if (query && query.trim() && (!image_url || !image_url.trim())) {
      // Sử dụng nearText thay vì hybrid
      searchResult = await tenant.query.hybrid(query, searchOptions);

      return {
        success: true,
        data: {
          objects: searchResult?.objects || [],
          searchType: "text",
          metadata: {
            total: searchResult?.objects?.length || 0,
            limit
          }
        },
      };
    }

    // Nếu không có query và không có image_url hoặc cả hai đều thất bại
    return {
      success: false,
      message: "Không có từ khóa tìm kiếm hoặc hình ảnh hợp lệ",
      data: {
        objects: [],
        searchType: "none",
      },
    };
  } catch (error: any) {
    return {
      success: false,
      message: `Lỗi khi tìm kiếm sản phẩm: ${error?.message || "Lỗi không xác định"}`,
      data: {
        objects: [],
        searchType: "error",
      },
    };
  }
};

/**
 * Lấy ID của sản phẩm dựa trên image_url và tenant_id
 * @param image_url URL hình ảnh của sản phẩm
 * @param tenant_id ID của tenant
 * @returns ID của sản phẩm hoặc null nếu không tìm thấy
 */
export const getProductIdByImageUrl = async ({
  image_url,
  tenant_id,
}: {
  image_url: string;
  tenant_id: string;
}) => {
  try {
    // Lấy collection Products
    const productsCollection = await client.collections.get("Products");
    const tenant = productsCollection.withTenant(tenant_id);

    // Tìm kiếm sản phẩm theo image_url
    const result = await tenant.query.nearText("product", {
      filters: tenant.filter.byProperty("image_url").equal(image_url),
      limit: 1,
    });

    // Kiểm tra kết quả
    if (result.objects && result.objects.length > 0 && result.objects[0].uuid) {
      return {
        success: true,
        id: result.objects[0].uuid,
        data: result.objects[0],
      };
    } else {
      return { success: false, message: "Không tìm thấy sản phẩm" };
    }
  } catch (error: any) {
    return {
      success: false,
      message: `Lỗi khi tìm kiếm sản phẩm: ${error?.message || "Lỗi không xác định"}`,
    };
  }
};

/**
 * Cập nhật thông tin sản phẩm dựa trên image_url
 * @param image_url URL hình ảnh của sản phẩm cần cập nhật
 * @param tenant_id ID của tenant
 * @param updateData Dữ liệu cập nhật
 */
export const updateProduct = async ({
  image_url,
  tenant_id,
  updateData,
}: {
  image_url: string;
  tenant_id: string;
  updateData: {
    name?: string;
    price?: number;
    stock?: number;
    description?: string;
    image_url?: string;
    bot_id?: string;
    is_active?: boolean;
  };
}) => {
  try {
    // Lấy ID của sản phẩm dựa trên image_url
    const productResult = await getProductIdByImageUrl({
      image_url,
      tenant_id,
    });

    if (!productResult.success || !productResult.id) {
      return productResult; // Trả về lỗi nếu không tìm thấy sản phẩm
    }

    const productId = productResult.id;

    // Lấy collection Products
    const productsCollection = await client.collections.get("Products");
    const tenant = productsCollection.withTenant(tenant_id);

    // Optimized bot_id processing
    let properties: any = { ...updateData };
    if ('bot_id' in updateData) {
      properties.bot_id = updateData.bot_id;
    }

    // Cập nhật sản phẩm
    const response = await tenant.data.update({
      id: productId,
      properties: properties,
    });

    return { success: true, data: response };
  } catch (error: any) {
    return {
      success: false,
      message: `Lỗi khi cập nhật sản phẩm: ${error?.message || "Lỗi không xác định"}`,
    };
  }
};

/**
 * Xóa sản phẩm dựa trên image_url
 * @param image_url URL hình ảnh của sản phẩm cần xóa
 * @param tenant_id ID của tenant
 */
export const deleteProduct = async ({
  image_url,
  tenant_id,
}: {
  image_url: string;
  tenant_id: string;
}) => {
  try {
    // Lấy ID của sản phẩm dựa trên image_url
    const productResult = await getProductIdByImageUrl({
      image_url,
      tenant_id,
    });

    if (!productResult.success || !productResult.id) {
      return productResult; // Trả về lỗi nếu không tìm thấy sản phẩm
    }

    const productId = productResult.id;

    // Lấy collection Products
    const productsCollection = await client.collections.get("Products");
    const tenant = productsCollection.withTenant(tenant_id);

    // Xóa sản phẩm theo ID
    const response = await tenant.data.deleteById(productId);

    return { success: true, data: response };
  } catch (error: any) {
    return {
      success: false,
      message: `Lỗi khi xóa sản phẩm: ${error?.message || "Lỗi không xác định"}`,
    };
  }
};

/**
 * Xóa nhiều sản phẩm theo bộ lọc
 * @param tenant_id ID của tenant
 * @param filter Bộ lọc để xác định sản phẩm cần xóa
 */
export const deleteProductsByFilter = async ({
  tenant_id,
  filter,
}: {
  tenant_id: string;
  filter: {
    property: string;
    operator: string;
    value: any;
  };
}) => {
  try {
    // Lấy collection Products
    const productsCollection = await client.collections.get("Products");
    const tenant = productsCollection.withTenant(tenant_id);

    // Tạo bộ lọc dựa trên thông tin được cung cấp
    let filterQuery;

    switch (filter.operator) {
      case "equal":
        filterQuery = tenant.filter
          .byProperty(filter.property)
          .equal(filter.value);
        break;
      case "notEqual":
        filterQuery = tenant.filter
          .byProperty(filter.property)
          .notEqual(filter.value);
        break;
      case "like":
        filterQuery = tenant.filter
          .byProperty(filter.property)
          .like(filter.value);
        break;
      default:
        throw new Error(`Toán tử không được hỗ trợ: ${filter.operator}`);
    }

    // Xóa nhiều sản phẩm theo bộ lọc
    const response = await tenant.data.deleteMany(filterQuery);

    return { success: true, data: response };
  } catch (error: any) {
    return {
      success: false,
      message: `Lỗi khi xóa sản phẩm theo bộ lọc: ${error?.message || "Lỗi không xác định"}`,
    };
  }
};

/**
 * Xóa sản phẩm theo product_id
 * @param product_id ID của sản phẩm cần xóa
 * @param tenant_id ID của tenant
 */
export const deleteProductByProductId = async ({
  product_id,
  tenant_id,
}: {
  product_id: string;
  tenant_id: string;
}) => {
  try {
    // Lấy collection Products
    const productsCollection = await client.collections.get("Products");
    const tenant = productsCollection.withTenant(tenant_id);

    // Tạo bộ lọc để tìm sản phẩm theo product_id
    const filterQuery = tenant.filter
      .byProperty("product_id")
      .equal(product_id);

    // Xóa sản phẩm theo product_id
    const response = await tenant.data.deleteMany(filterQuery);
    // Kiểm tra xem có sản phẩm nào bị xóa không
    if (response && response.successful > 0) {
      return { success: true, data: response };
    } else {
      return {
        success: false,
        message: `Không tìm thấy sản phẩm với product_id: ${product_id}`,
      };
    }
  } catch (error: any) {
    return {
      success: false,
      message: `Lỗi khi xóa sản phẩm theo product_id: ${error?.message || "Lỗi không xác định"}`,
    };
  }
};

/**
 * Xóa nhiều sản phẩm theo danh sách product_id
 * @param product_ids Mảng các ID sản phẩm cần xóa
 * @param tenant_id ID của tenant
 */
export const deleteProductsByProductIds = async ({
  product_ids,
  tenant_id,
}: {
  product_ids: string[];
  tenant_id: string;
}) => {
  try {
    if (!product_ids || product_ids.length === 0) {
      return {
        success: false,
        message: "Danh sách product_id không được để trống",
      };
    }

    // Lấy collection Products
    const productsCollection = await client.collections.get("Products");
    const tenant = productsCollection.withTenant(tenant_id);

    // Tạo bộ lọc cho nhiều product_id bằng cách sử dụng OR
    const filters = product_ids.map(id => 
      tenant.filter.byProperty("product_id").equal(id)
    );

    // Kết hợp các bộ lọc bằng OR
    let combinedFilter = filters[0];
    for (let i = 1; i < filters.length; i++) {
      combinedFilter = tenant.filter.or(combinedFilter, filters[i]);
    }

    // Xóa sản phẩm theo danh sách product_id
    const response = await tenant.data.deleteMany(combinedFilter);
    
    // Kiểm tra kết quả
    if (response && response.successful > 0) {
      return { 
        success: true, 
        data: response,
        message: `Đã xóa thành công ${response.successful} sản phẩm từ ${product_ids.length} sản phẩm được yêu cầu`
      };
    } else {
      return {
        success: false,
        message: `Không tìm thấy sản phẩm nào với danh sách product_id được cung cấp`,
        data: response
      };
    }
  } catch (error: any) {
    return {
      success: false,
      message: `Lỗi khi xóa nhiều sản phẩm: ${error?.message || "Lỗi không xác định"}`,
    };
  }
};

/**
 * Cập nhật thông tin sản phẩm theo product_id
 * @param product_id ID của sản phẩm cần cập nhật
 * @param tenant_id ID của tenant
 * @param updateData Dữ liệu cập nhật cho sản phẩm
 */
export const updateProductByProductId = async ({
  product_id,
  tenant_id,
  updateData,
}: {
  product_id: string;
  tenant_id: string;
  updateData: {
    name?: string;
    price?: number;
    stock?: number;
    description?: string;
    image_url?: string;
    bot_id?: string;
    is_active?: boolean;
  };
}) => {
  try {
    // Lấy collection Products
    const productsCollection = await client.collections.get("Products");
    const tenant = productsCollection.withTenant(tenant_id);

    // Tạo bộ lọc để tìm sản phẩm theo product_id
    const filterQuery = tenant.filter
      .byProperty("product_id")
      .equal(product_id);

    // Tìm kiếm các sản phẩm phù hợp để lấy ID
    const searchResult = await tenant.query.fetchObjects({
      filters: filterQuery,
    });

    // Kiểm tra xem có sản phẩm nào được tìm thấy không
    if (!searchResult.objects || searchResult.objects.length === 0) {
      return {
        success: false,
        message: `Không tìm thấy sản phẩm với product_id: ${product_id}`,
      };
    }

    // Optimized bot_id processing
    let processedUpdateData = { ...updateData };
    if ('bot_id' in updateData) {
      processedUpdateData.bot_id = updateData.bot_id;
    }

    // Cập nhật thông tin cho từng sản phẩm tìm thấy
    const updatePromises = searchResult.objects.map(async (obj: any) => {
      if (obj.uuid) {
        return await tenant.data.update({
          id: obj.uuid,
          properties: processedUpdateData,
        });
      }
      return null;
    });

    // Chờ tất cả các cập nhật hoàn thành
    const updateResults = await Promise.all(updatePromises);
    const validResults = updateResults.filter((result) => result !== null);

    if (validResults.length > 0) {
      return {
        success: true,
        data: validResults,
        message: `Đã cập nhật thông tin cho ${validResults.length} sản phẩm với product_id: ${product_id}`,
      };
    } else {
      return {
        success: false,
        message: `Không thể cập nhật thông tin cho sản phẩm với product_id: ${product_id}`,
      };
    }
  } catch (error: any) {
    return {
      success: false,
      message: `Lỗi khi cập nhật thông tin sản phẩm: ${error?.message || "Lỗi không xác định"}`,
    };
  }
};

/**
 * Cập nhật trường bot_id của sản phẩm (thêm hoặc xóa bot_id)
 * @param product_id ID của sản phẩm cần cập nhật
 * @param tenant_id ID của tenant
 * @param bot_id ID của bot cần thêm hoặc xóa
 * @param action Hành động: 'add' để thêm bot_id, 'remove' để xóa bot_id
 */
export const updateProductBotId = async ({
  product_id,
  tenant_id,
  bot_id,
  action,
}: {
  product_id: string;
  tenant_id: string;
  bot_id: string;
  action: "add" | "remove";
}) => {
  try {
    // Lấy collection Products
    const productsCollection = await client.collections.get("Products");
    const tenant = productsCollection.withTenant(tenant_id);

    // Tạo bộ lọc để tìm sản phẩm theo product_id
    const filterQuery = tenant.filter
      .byProperty("product_id")
      .equal(product_id);

    // Tìm kiếm sản phẩm phù hợp để lấy ID và thông tin hiện tại
    const searchResult = await tenant.query.fetchObjects({
      filters: filterQuery,
      properties: ["bot_id"],
    });

    // Kiểm tra xem có sản phẩm nào được tìm thấy không
    if (!searchResult.objects || searchResult.objects.length === 0) {
      return {
        success: false,
        message: `Không tìm thấy sản phẩm với product_id: ${product_id}`,
      };
    }

    // Xử lý cập nhật bot_id cho từng sản phẩm tìm thấy
    const updatePromises = searchResult.objects.map(async (obj: any) => {
      if (obj.uuid) {
        // Lấy bot_id hiện tại
        let currentBotId: string = obj.properties?.bot_id || '';

        let updatedBotId: string;

        // Thực hiện thêm hoặc xóa bot_id
        if (action === "add") {
          // Đặt bot_id mới (chỉ một giá trị)
          updatedBotId = bot_id;
        } else if (action === "remove") {
          // Xóa bot_id bằng cách đặt thành chuỗi rỗng
          updatedBotId = currentBotId === bot_id ? '' : currentBotId;
        } else {
          updatedBotId = currentBotId;
        }

        // Cập nhật sản phẩm với bot_id mới
        return await tenant.data.update({
          id: obj.uuid,
          properties: {
            bot_id: updatedBotId,
          },
        });
      }
      return null;
    });

    // Chờ tất cả các cập nhật hoàn thành
    const updateResults = await Promise.all(updatePromises);
    const validResults = updateResults.filter((result) => result !== null);

    if (validResults.length > 0) {
      return {
        success: true,
        data: validResults,
        message: `Đã ${action === "add" ? "thêm" : "xóa"} bot_id ${bot_id} cho ${validResults.length} sản phẩm với product_id: ${product_id}`,
      };
    } else {
      return {
        success: false,
        message: `Không thể cập nhật bot_id cho sản phẩm với product_id: ${product_id}`,
      };
    }
  } catch (error: any) {
    return {
      success: false,
      message: `Lỗi khi cập nhật bot_id sản phẩm: ${error?.message || "Lỗi không xác định"}`,
    };
  }
};

/**
 * Health check cho service
 */
export const healthCheck = async () => {
  try {
    const collection = await client.collections.get("Products");
    const exists = await collection.exists();
    
    return {
      success: true,
      status: "healthy",
      details: {
        collection_exists: exists,
        timestamp: new Date().toISOString()
      }
    };
  } catch (error: any) {
    return {
      success: false,
      status: "unhealthy",
      error: error.message
    };
  }
};

/**
 * Batch operations cho hiệu suất cao
 */
export const batchOperations = {
  async updateMultipleProducts(updates: Array<{
    product_id: string;
    tenant_id: string;
    updateData: {
      name?: string;
      price?: number;
      stock?: number;
      description?: string;
      image_url?: string;
      bot_id?: string;
      is_active?: boolean;
    };
  }>) {
    const results = await Promise.all(
      updates.map(update => updateProductByProductId(update))
    );
    
    const successful = results.filter(r => r.success).length;
    const failed = results.length - successful;
    
    return {
      success: successful > 0,
      results,
      stats: { successful, failed, total: results.length }
    };
  },

  async deleteMultipleProducts(deletions: Array<{
    product_id: string;
    tenant_id: string;
  }>) {
    const results = await Promise.all(
      deletions.map(deletion => deleteProductByProductId(deletion))
    );
    
    const successful = results.filter(r => r.success).length;
    const failed = results.length - successful;
    
    return {
      success: successful > 0,
      results,
      stats: { successful, failed, total: results.length }
    };
  }
};
