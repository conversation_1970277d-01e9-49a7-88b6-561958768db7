/**
 * Message Buffer Service - Qu<PERSON>n lý việc gom tin nhắn và delayed jobs
 * Triển khai <PERSON>ơng án 1: Delayed Jobs + Message Aggregation
 */
import { Queue } from 'bullmq';
import { QUEUE_NAMES, queueConfigs } from './config';
import { messageBufferRedisConnection } from './config';
import { cancelFollowUpJobs } from './follow-up.service';
import Redis from 'ioredis';

// Interface cho Message trong buffer
export interface BufferedMessage {
  id: string;
  content: string;
  attachments?: any[];
  timestamp: Date;
  messageType: string;
  resourceId: string;
  threadId: string;
  conversationId: string;
  accountId: string;
  inboxId: string;
  metadata?: any;
}

// Interface cho Message Batch
export interface MessageBatch {
  threadId: string;
  resourceId: string;
  botId: string;
  tenantId: string;
  messages: BufferedMessage[];
  scheduledJobId?: string;
  timeout: number; // ms
  createdAt: Date;
  lastMessageAt: Date;
  chatbotInfo?: any;
  accountId: string;
  conversationId: string;
  inboxId: string;
}

// Interface cho Job Data
export interface MessageBatchJobData {
  batchKey: string;
  batch: MessageBatch;
  immediate?: boolean; // Flag để xử lý ngay lập tức
}

class MessageBufferService {
  private redis!: Redis;
  private messageBatchingQueue!: Queue;
  private readonly BUFFER_PREFIX = 'msg_buffer:';
  private readonly JOB_ID_PREFIX = 'batch_job:';
  private isRedisConnected: boolean = false;
  private connectionRetryCount: number = 0;
  private readonly MAX_RETRY_COUNT = 3;

  constructor() {
    this.initializeConnections();
  }

  private async initializeConnections() {
    try {
      // Sử dụng cấu hình Redis riêng cho message buffering
      this.redis = new Redis(messageBufferRedisConnection as any);

      // Setup Redis event handlers
      this.redis.on('connect', () => {
        this.isRedisConnected = true;
        this.connectionRetryCount = 0;
      });

      this.redis.on('ready', () => {
        this.isRedisConnected = true;
      });

      this.redis.on('error', (error) => {
        console.error('❌ Redis connection error in MessageBufferService:', error);
        this.isRedisConnected = false;
      });

      this.redis.on('close', () => {
        console.warn('⚠️ MessageBufferService Redis connection closed');
        this.isRedisConnected = false;
      });

      this.redis.on('reconnecting', () => {
        this.connectionRetryCount++;
      });

      // Tạo message batching queue
      this.messageBatchingQueue = new Queue(
        QUEUE_NAMES.MESSAGE_BATCHING,
        queueConfigs[QUEUE_NAMES.MESSAGE_BATCHING]
      );

      this.messageBatchingQueue.on('error', (error) => {
        console.error('❌ Message batching queue error:', error);
      });

      // Kiểm tra kết nối ban đầu
      await this.checkRedisConnection();

    } catch (error) {
      console.error('❌ Lỗi khởi tạo MessageBufferService:', error);
      this.isRedisConnected = false;
    }
  }

  /**
   * Kiểm tra và đảm bảo Redis connection
   */
  private async checkRedisConnection(): Promise<boolean> {
    try {
      if (!this.redis) {
        return false;
      }

      // Kiểm tra trạng thái kết nối
      if (this.redis.status !== 'ready' && this.redis.status !== 'connecting') {
        await this.redis.connect();
      }

      // Test ping
      await this.redis.ping();
      this.isRedisConnected = true;
      return true;
    } catch (error) {
      console.error('❌ Redis connection check failed:', error);
      this.isRedisConnected = false;
      return false;
    }
  }

  /**
   * Thực hiện Redis operation với retry logic
   */
  private async executeRedisOperation<T>(operation: () => Promise<T>): Promise<T | null> {
    try {
      // Kiểm tra kết nối trước khi thực hiện operation
      if (!this.isRedisConnected) {
        const connectionOk = await this.checkRedisConnection();
        if (!connectionOk) {
          throw new Error('Redis connection not available');
        }
      }

      return await operation();
    } catch (error: any) {
      console.error('❌ Redis operation failed:', error);

      // Nếu là lỗi kết nối và chưa vượt quá số lần retry
      if (this.connectionRetryCount < this.MAX_RETRY_COUNT &&
          (error.message?.includes('Stream isn\'t writeable') ||
           error.message?.includes('Connection is closed') ||
           error.message?.includes('ECONNREFUSED'))) {


        try {
          await this.checkRedisConnection();
          return await operation();
        } catch (retryError) {
          console.error('❌ Retry Redis operation failed:', retryError);
        }
      }

      return null;
    }
  }

  /**
   * Tạo key cho buffer dựa trên threadId và resourceId
   */
  private getBatchKey(threadId: string, resourceId: string): string {
    return `${this.BUFFER_PREFIX}${threadId}:${resourceId}`;
  }

  /**
   * Tạo key cho job ID
   */
  private getJobIdKey(batchKey: string): string {
    return `${this.JOB_ID_PREFIX}${batchKey}`;
  }

  /**
   * Lấy cấu hình timeout cho bot từ database hoặc mặc định cho hình ảnh
   */
  private getBotTimeout(chatbotInfo: any, hasImages: boolean = false): number {
    // Nếu có hình ảnh, mặc định 10s để chờ user nhập thêm câu hỏi
    if (hasImages) {
      const imageDelaySeconds = 10; // Mặc định 10s cho tin nhắn hình ảnh
      return imageDelaySeconds * 1000;
    }

    // Lấy delay_time từ chatbot_configurations.instruction.delay_time (giây) cho tin nhắn text
    // Cập nhật: Mặc định 2s thay vì 0s để tránh xử lý ngay lập tức
    const delayTimeSeconds = chatbotInfo?.instruction?.delay_time ?? 2; // Default 2s cho tin nhắn text

    // Đảm bảo delay_time trong khoảng hợp lệ (0-30 giây)
    const validDelayTime = Math.max(0, Math.min(30, delayTimeSeconds));

    if (validDelayTime === 0) {
    } else {
    }

    return validDelayTime * 1000;
  }

  /**
   * Kiểm tra xem tin nhắn có cần xử lý ngay lập tức không
   * Cập nhật logic: Chỉ xử lý ngay lập tức khi có từ khóa khẩn cấp
   * Loại bỏ xử lý ngay lập tức dựa trên delay_time = 0 vì mặc định đã là 2s
   */
  private async shouldProcessImmediately(
    message: BufferedMessage,
    chatbotInfo: any,
    existingBatch: MessageBatch | null
  ): Promise<boolean> {
    const content = message.content?.toLowerCase() || '';

    // Từ khóa cần xử lý ngay lập tức
    const urgentKeywords = ['urgent', 'gấp', 'khẩn cấp', 'emergency', 'hủy đơn', 'cancel order', 'hủy', 'cancel'];

    // Kiểm tra từ khóa khẩn cấp - luôn xử lý ngay lập tức
    const hasUrgentKeyword = urgentKeywords.some(keyword => content.includes(keyword));
    if (hasUrgentKeyword) {
      return true;
    }

    // Kiểm tra có hình ảnh - tin nhắn hình ảnh KHÔNG bao giờ xử lý ngay lập tức
    // để chờ user nhập thêm câu hỏi tư vấn
    const hasImages = Boolean(message.attachments && message.attachments.length > 0);
    if (hasImages) {
      return false;
    }

    // Kiểm tra có batch đang chờ xử lý không
    if (existingBatch && existingBatch.messages.length > 0) {
      return false;
    }

    // Cập nhật: Chỉ xử lý ngay lập tức khi delay_time được cấu hình rõ ràng = 0
    // Nếu không có cấu hình (undefined/null) thì sử dụng mặc định 2s (không xử lý ngay)
    const delayTimeSeconds = chatbotInfo?.instruction?.delay_time;
    const isExplicitImmediateMode = delayTimeSeconds === 0;

    if (isExplicitImmediateMode) {
    }

    return isExplicitImmediateMode;
  }

  /**
   * Thêm tin nhắn vào buffer và quản lý delayed job
   */
  async addMessageToBuffer(
    message: BufferedMessage,
    chatbotInfo: any,
    tenantId: string,
    botId: string
  ): Promise<{ success: boolean; immediate: boolean; jobId?: string }> {
    try {
      const batchKey = this.getBatchKey(message.threadId, message.resourceId);
      const jobIdKey = this.getJobIdKey(batchKey);

      // Kiểm tra có hình ảnh để áp dụng delay mặc định
      const hasImages = Boolean(message.attachments && message.attachments.length > 0);
      const timeout = this.getBotTimeout(chatbotInfo, hasImages);

      // Lấy batch hiện tại từ Redis với retry logic
      const existingBatchData = await this.executeRedisOperation(async () => {
        return await this.redis.get(batchKey);
      });

      let existingBatch: MessageBatch | null = null;
      if (existingBatchData) {
        existingBatch = JSON.parse(existingBatchData);
      }

      // Kiểm tra xem có cần xử lý ngay lập tức không (sau khi đã có thông tin về existing batch)
      const immediate = await this.shouldProcessImmediately(message, chatbotInfo, existingBatch);

      let batch: MessageBatch;

      if (existingBatch) {
        batch = existingBatch;
        batch.messages.push(message);
        batch.lastMessageAt = new Date();
      } else {
        // Tạo batch mới
        batch = {
          threadId: message.threadId,
          resourceId: message.resourceId,
          botId,
          tenantId,
          messages: [message],
          timeout,
          createdAt: new Date(),
          lastMessageAt: new Date(),
          chatbotInfo,
          accountId: message.accountId,
          conversationId: message.conversationId,
          inboxId: message.inboxId,
        };
      }

      // Hủy job cũ nếu có
      const existingJobId = await this.executeRedisOperation(async () => {
        return await this.redis.get(jobIdKey);
      });

      if (existingJobId) {
        try {
          await this.messageBatchingQueue.remove(existingJobId as string);
        } catch (error) {
          console.warn(`⚠️ Không thể hủy job cũ ${existingJobId}:`, error);
        }
      }

      // Hủy follow-up jobs khi khách hàng gửi tin nhắn mới
      await this.handleCustomerInteraction(message.conversationId, tenantId);

      // Lưu batch vào Redis với TTL và retry logic
      const batchTTL = Math.max(timeout * 2, 300000); // Tối thiểu 5 phút
      const saveResult = await this.executeRedisOperation(async () => {
        return await this.redis.setex(batchKey, Math.floor(batchTTL / 1000), JSON.stringify(batch));
      });

      if (!saveResult) {
        throw new Error('Không thể lưu batch vào Redis');
      }

      let jobId: string | undefined;

      if (immediate) {
        // Xử lý ngay lập tức
        const job = await this.messageBatchingQueue.add(
          'process-message-batch',
          {
            batchKey,
            batch,
            immediate: true,
          } as MessageBatchJobData,
          {
            priority: 20, // Ưu tiên cao nhất
            delay: 0,     // Không delay
          }
        );
        jobId = job.id;
      } else {
        // Tạo delayed job mới
        // Với mặc định mới là 2s, không cần logic đặc biệt cho timeout = 0
        // vì timeout tối thiểu đã là 2s (2000ms)
        const actualDelay = timeout;

        const job = await this.messageBatchingQueue.add(
          'process-message-batch',
          {
            batchKey,
            batch,
            immediate: false,
          } as MessageBatchJobData,
          {
            delay: actualDelay,
            priority: 15,
          }
        );
        jobId = job.id;
        batch.scheduledJobId = jobId;

        // Lưu job ID vào Redis với retry logic
        await this.executeRedisOperation(async () => {
          return await this.redis.setex(jobIdKey, Math.floor(batchTTL / 1000), jobId || '');
        });

      }

      // Cập nhật batch với job ID
      await this.executeRedisOperation(async () => {
        return await this.redis.setex(batchKey, Math.floor(batchTTL / 1000), JSON.stringify(batch));
      });

      return {
        success: true,
        immediate,
        jobId,
      };

    } catch (error) {
      console.error('❌ Lỗi khi thêm tin nhắn vào buffer:', error);
      return {
        success: false,
        immediate: false,
      };
    }
  }

  /**
   * Lấy batch từ Redis
   */
  async getBatch(batchKey: string): Promise<MessageBatch | null> {
    try {
      const batchData = await this.executeRedisOperation(async () => {
        return await this.redis.get(batchKey);
      });

      if (!batchData) return null;
      return JSON.parse(batchData);
    } catch (error) {
      console.error('❌ Lỗi khi lấy batch:', error);
      return null;
    }
  }

  /**
   * Xóa batch khỏi Redis sau khi xử lý
   */
  async clearBatch(batchKey: string): Promise<void> {
    try {
      const jobIdKey = this.getJobIdKey(batchKey);

      await this.executeRedisOperation(async () => {
        return await Promise.all([
          this.redis.del(batchKey),
          this.redis.del(jobIdKey),
        ]);
      });

    } catch (error) {
      console.error('❌ Lỗi khi xóa batch:', error);
    }
  }

  /**
   * Lấy thống kê buffer
   */
  async getBufferStats(): Promise<{
    totalBatches: number;
    totalMessages: number;
    avgMessagesPerBatch: number;
  }> {
    try {
      const batchKeys = await this.executeRedisOperation(async () => {
        return await this.redis.keys(`${this.BUFFER_PREFIX}*`);
      });

      if (!batchKeys) {
        return {
          totalBatches: 0,
          totalMessages: 0,
          avgMessagesPerBatch: 0,
        };
      }

      let totalMessages = 0;

      for (const key of batchKeys) {
        const batchData = await this.executeRedisOperation(async () => {
          return await this.redis.get(key);
        });

        if (batchData) {
          const batch: MessageBatch = JSON.parse(batchData);
          totalMessages += batch.messages.length;
        }
      }

      return {
        totalBatches: batchKeys.length,
        totalMessages,
        avgMessagesPerBatch: batchKeys.length > 0 ? totalMessages / batchKeys.length : 0,
      };
    } catch (error) {
      console.error('❌ Lỗi khi lấy thống kê buffer:', error);
      return {
        totalBatches: 0,
        totalMessages: 0,
        avgMessagesPerBatch: 0,
      };
    }
  }

  /**
   * Cleanup expired batches (chạy định kỳ)
   */
  async cleanupExpiredBatches(): Promise<number> {
    try {
      const batchKeys = await this.executeRedisOperation(async () => {
        return await this.redis.keys(`${this.BUFFER_PREFIX}*`);
      });

      if (!batchKeys) {
        return 0;
      }

      let cleanedCount = 0;
      const now = new Date();

      for (const key of batchKeys) {
        const batchData = await this.executeRedisOperation(async () => {
          return await this.redis.get(key);
        });

        if (batchData) {
          const batch: MessageBatch = JSON.parse(batchData);
          const batchAge = now.getTime() - new Date(batch.lastMessageAt).getTime();

          // Xóa batch cũ hơn 1 giờ
          if (batchAge > 3600000) {
            await this.clearBatch(key.replace(this.BUFFER_PREFIX, ''));
            cleanedCount++;
          }
        }
      }

      if (cleanedCount > 0) {
      }

      return cleanedCount;
    } catch (error) {
      console.error('❌ Lỗi khi cleanup expired batches:', error);
      return 0;
    }
  }

  /**
   * Kiểm tra trạng thái kết nối Redis
   */
  async getConnectionStatus(): Promise<{
    isConnected: boolean;
    redisStatus: string;
    retryCount: number;
  }> {
    return {
      isConnected: this.isRedisConnected,
      redisStatus: this.redis?.status || 'unknown',
      retryCount: this.connectionRetryCount,
    };
  }

  /**
   * Xử lý khi khách hàng tương tác (gửi tin nhắn mới)
   * Hủy tất cả follow-up jobs hiện tại
   */
  private async handleCustomerInteraction(conversationId: string, tenantId: string): Promise<void> {
    try {

      const cancelResult = await cancelFollowUpJobs({
        conversation_id: conversationId,
        tenant_id: tenantId,
        reason: 'Customer sent new message',
      });

      if (cancelResult.success) {
      } else {
      }
    } catch (error: any) {
      console.error('❌ Lỗi khi hủy follow-up jobs:', error);
      // Không throw error để không làm fail message processing
    }
  }

  /**
   * Đóng kết nối
   */
  async close(): Promise<void> {
    try {
      await Promise.all([
        this.redis?.quit(),
        this.messageBatchingQueue?.close(),
      ]);
    } catch (error) {
      console.error('❌ Lỗi khi đóng MessageBufferService connections:', error);
    }
  }
}

// Export singleton instance với lazy initialization
let messageBufferServiceInstance: MessageBufferService | null = null;

export const getMessageBufferService = (): MessageBufferService => {
  if (!messageBufferServiceInstance) {
    messageBufferServiceInstance = new MessageBufferService();
  }
  return messageBufferServiceInstance;
};

// Export singleton instance
export const messageBufferService = getMessageBufferService();
export default messageBufferService;