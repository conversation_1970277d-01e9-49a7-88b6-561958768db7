import { createWorkflow, createStep } from "@mastra/core/workflows";
import { z } from "zod";
import { keywordAnalysisAgent, ragAgent } from "../agents/loma-customer-service";
import { searchFaqs } from "../../services/weaviate/faq.service";

// Schema cho input của workflow
const workflowInputSchema = z.object({
  recentMessages: z.array(z.object({
    role: z.enum(["user", "assistant", "system"]),
    content: z.string(),
    timestamp: z.string().optional()
  })),
  newMessage: z.string(),
  tenant_id: z.string(),
  bot_id: z.string().optional()
});

// Schema cho output của workflow
const workflowOutputSchema = z.object({
  response: z.string(),
  keywords_used: z.array(z.string()),
  faq_context: z.array(z.object({
    topic: z.string(),
    content: z.string(),
    score: z.number().optional()
  })),
  main_topic: z.string(),
  customer_intent: z.string()
});

// Step 1: <PERSON><PERSON> tích tin nhắn và tạo keywords
const keywordAnalysisStep = createStep({
  id: "keyword-analysis",
  description: "Phân tích tin nhắn và tạo keywords để tìm kiếm FAQ",
  inputSchema: workflowInputSchema,
  outputSchema: z.object({
    keywords: z.array(z.string()),
    main_topic: z.string(),
    customer_intent: z.string(),
    analysis_prompt: z.string()
  }),
  execute: async ({ inputData }) => {
    const { recentMessages, newMessage } = inputData;
    
    // Tạo prompt cho agent phân tích
    const conversationHistory = recentMessages
      .slice(-5) // Lấy 5 tin nhắn gần nhất
      .map(msg => `${msg.role}: ${msg.content}`)
      .join('\n');
    
    const analysisPrompt = `
Phân tích cuộc hội thoại sau để tạo keywords tìm kiếm FAQ:

=== 5 TIN NHẮN GẦN NHẤT ===
${conversationHistory}

=== TIN NHẮN MỚI NHẤT ===
user: ${newMessage}

Hãy phân tích và trả về JSON với keywords phù hợp để tìm kiếm FAQ.
`;

    try {
      // Gọi agent phân tích keywords
      const result = await keywordAnalysisAgent.generate([
        { role: "user", content: analysisPrompt }
      ]);

      // Parse JSON response từ agent
      let analysisResult;
      try {
        const jsonMatch = result.text.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          analysisResult = JSON.parse(jsonMatch[0]);
        } else {
          throw new Error("Không tìm thấy JSON trong response");
        }
      } catch (parseError) {
        console.error("Lỗi parse JSON từ keyword analysis agent:", parseError);
        // Fallback: tạo keywords từ tin nhắn mới
        const fallbackKeywords = newMessage
          .toLowerCase()
          .split(' ')
          .filter(word => word.length > 2)
          .slice(0, 3);
        
        analysisResult = {
          keywords: fallbackKeywords.length > 0 ? fallbackKeywords : ["túi vải", "giá cả", "tư vấn"],
          main_topic: "Tư vấn chung",
          customer_intent: "Tìm hiểu thông tin"
        };
      }

      return {
        keywords: analysisResult.keywords || [],
        main_topic: analysisResult.main_topic || "Tư vấn chung",
        customer_intent: analysisResult.customer_intent || "Tìm hiểu thông tin",
        analysis_prompt: analysisPrompt
      };
    } catch (error) {
      console.error("Lỗi trong keyword analysis step:", error);
      
      // Fallback: tạo keywords đơn giản từ tin nhắn mới
      const fallbackKeywords = newMessage
        .toLowerCase()
        .split(' ')
        .filter(word => word.length > 2)
        .slice(0, 3);
      
      return {
        keywords: fallbackKeywords.length > 0 ? fallbackKeywords : ["túi vải", "giá cả", "tư vấn"],
        main_topic: "Tư vấn chung",
        customer_intent: "Tìm hiểu thông tin",
        analysis_prompt: analysisPrompt
      };
    }
  }
});

// Step 2: Tìm kiếm FAQ dựa trên keywords
const faqSearchStep = createStep({
  id: "faq-search",
  description: "Tìm kiếm FAQ dựa trên keywords đã phân tích",
  inputSchema: z.object({
    keywords: z.array(z.string()),
    main_topic: z.string(),
    customer_intent: z.string(),
    analysis_prompt: z.string(),
    tenant_id: z.string(),
    bot_id: z.string().optional()
  }),
  outputSchema: z.object({
    faq_results: z.array(z.object({
      topic: z.string(),
      content: z.string(),
      score: z.number().optional()
    })),
    keywords_used: z.array(z.string()),
    main_topic: z.string(),
    customer_intent: z.string()
  }),
  execute: async ({ inputData }) => {
    const { keywords, main_topic, customer_intent, tenant_id, bot_id } = inputData;
    
    let allFaqResults: any[] = [];
    const usedKeywords: string[] = [];

    try {
      // Tìm kiếm FAQ cho từng keyword
      for (const keyword of keywords) {
        try {
          const searchResult = await searchFaqs(keyword, tenant_id, 2, bot_id);
          
          if (searchResult.success && searchResult.data?.objects) {
            const faqData = searchResult.data.objects.map((obj: any) => ({
              topic: obj.properties?.topic || "",
              content: obj.properties?.content || "",
              score: obj.metadata?.score || 0
            }));
            
            allFaqResults.push(...faqData);
            usedKeywords.push(keyword);
          }
        } catch (searchError) {
          console.error(`Lỗi tìm kiếm FAQ cho keyword "${keyword}":`, searchError);
        }
      }

      // Loại bỏ duplicate và sắp xếp theo score
      const uniqueFaqs = allFaqResults
        .filter((faq, index, self) => 
          index === self.findIndex(f => f.topic === faq.topic && f.content === faq.content)
        )
        .sort((a, b) => (b.score || 0) - (a.score || 0))
        .slice(0, 3); // Lấy tối đa 3 FAQ

      return {
        faq_results: uniqueFaqs,
        keywords_used: usedKeywords,
        main_topic,
        customer_intent
      };
    } catch (error) {
      console.error("Lỗi trong FAQ search step:", error);
      
      return {
        faq_results: [],
        keywords_used: [],
        main_topic,
        customer_intent
      };
    }
  }
});

// Step 3: Tạo response với context từ FAQ
const responseGenerationStep = createStep({
  id: "response-generation",
  description: "Tạo response sử dụng Loma Bag agent với context từ FAQ",
  inputSchema: z.object({
    faq_results: z.array(z.object({
      topic: z.string(),
      content: z.string(),
      score: z.number().optional()
    })),
    keywords_used: z.array(z.string()),
    main_topic: z.string(),
    customer_intent: z.string()
  }),
  outputSchema: workflowOutputSchema,
  execute: async ({ inputData, getInitData }) => {
    const { faq_results, keywords_used, main_topic, customer_intent } = inputData;
    const initData = getInitData();
    const { newMessage, recentMessages } = initData;

    try {
      // Tạo context từ FAQ results
      const faqContext = faq_results.length > 0 
        ? faq_results.map(faq => `**${faq.topic}**: ${faq.content}`).join('\n\n')
        : "";

      // Tạo prompt cho Loma Bag agent với context
      const contextualPrompt = faqContext 
        ? `
=== THÔNG TIN FAQ LIÊN QUAN ===
${faqContext}

=== TIN NHẮN KHÁCH HÀNG ===
${newMessage}

Hãy sử dụng thông tin FAQ ở trên để trả lời khách hàng một cách chính xác và hữu ích.
`
        : newMessage;

      // Tạo conversation history cho agent
      const conversationForAgent = [
        ...recentMessages.slice(-5).map((msg: any) => ({
          role: msg.role as "user" | "assistant" | "system",
          content: msg.content
        })),
        { role: "user" as const, content: contextualPrompt }
      ];

      // Gọi Loma Bag agent để tạo response
      const agentResult = await ragAgent.generate(conversationForAgent);

      return {
        response: agentResult.text,
        keywords_used,
        faq_context: faq_results,
        main_topic,
        customer_intent
      };
    } catch (error) {
      console.error("Lỗi trong response generation step:", error);
      
      // Fallback: gọi agent không có context
      try {
        const fallbackResult = await ragAgent.generate([
          { role: "user", content: newMessage }
        ]);
        
        return {
          response: fallbackResult.text,
          keywords_used,
          faq_context: faq_results,
          main_topic,
          customer_intent
        };
      } catch (fallbackError) {
        console.error("Lỗi trong fallback response:", fallbackError);

        // KHÔNG gửi tin nhắn lỗi cho khách hàng - throw error để hệ thống xử lý
        throw new Error('Workflow failed - không gửi tin nhắn cho khách hàng');
      }
    }
  }
});

// Tạo workflow chính
export const lomaBagWorkflow = createWorkflow({
  id: "loma-bag-workflow",
  description: "Workflow tư vấn Loma Bag với tìm kiếm FAQ context",
  inputSchema: workflowInputSchema,
  outputSchema: workflowOutputSchema
})
  .then(keywordAnalysisStep)
  .map(async ({ getStepResult, getInitData }) => {
    const keywordResult = getStepResult(keywordAnalysisStep);
    const initData = getInitData();

    return {
      ...keywordResult,
      tenant_id: initData.tenant_id,
      bot_id: initData.bot_id
    };
  })
  .then(faqSearchStep)
  .then(responseGenerationStep)
  .commit();
