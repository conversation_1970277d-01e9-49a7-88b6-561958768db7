import { createTool } from "@mastra/core/tools";
import { z } from "zod";

/**
 * Công cụ gửi hình ảnh cho khách hàng
 * Tool này chỉ để bắt sự kiện, logic gửi hình ảnh thực tế được xử lý bởi onStepFinish trong agent.service.ts
 */
export const sendImagesLinkTool = createTool({
  id: "sendImagesLinkTool",
  description: "Dùng khi muốn gửi các hình ảnh cho khách hàng",
  inputSchema: z.object({
    images: z.array(z.string()).describe("Danh sách URL của các hình ảnh cần gửi cho khách hàng"),
  }),
  execute: async ({ context, runtimeContext }) => {
    try {
      console.log("Tool send_images_link được gọi với:", context);

      // Lấy tenant_id từ runtime context
      const tenant_id = runtimeContext.get("tenant_id");

      if (!tenant_id) {
        return {
          success: false,
          error: "<PERSON>hiếu thông tin tenant_id trong runtime context",
        };
      }

      // Validate danh sách hình ảnh
      if (!context.images || !Array.isArray(context.images) || context.images.length === 0) {
        return {
          success: false,
          error: "Danh sách hình ảnh không hợp lệ hoặc rỗng",
        };
      }

      // Tool này chỉ để bắt sự kiện, logic gửi hình ảnh thực tế sẽ được xử lý bởi onStepFinish trong agent.service.ts
      console.log(`📸 Tool send_images_link: Đã chuẩn bị ${context.images.length} hình ảnh để gửi`);

      return {
        success: true,
        images: context.images, // Truyền images để onStepFinish có thể xử lý
        message: `Đã chuẩn bị ${context.images.length} hình ảnh để gửi cho khách hàng`,
      };
    } catch (error: any) {
      console.error("Lỗi trong tool send_images_link:", error);
      return {
        success: false,
        error: `Lỗi trong tool send_images_link: ${error?.message || "Lỗi không xác định"}`,
      };
    }
  },
});